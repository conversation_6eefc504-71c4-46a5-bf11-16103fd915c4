# Generated by Django 4.2.7 on 2025-08-11 05:28

from django.db import migrations, models


class Migration(migrations.Migration):

    dependencies = [
        ('erp', '0042_alter_customfield_target_model_invoice_and_more'),
    ]

    operations = [
        migrations.AlterField(
            model_name='attachment',
            name='category',
            field=models.CharField(choices=[('project_trial_balance', '试算表'), ('project_other', '项目其他'), ('contract_signed_scan', '合同双章扫描件'), ('contract_final_word', '合同终稿word版'), ('sales_invoice_scan', '销售发票扫描件'), ('receipt_confirmation', '签收单'), ('acceptance_report', '验收报告'), ('purchase_invoice_scan', '采购发票扫描件'), ('contract_other', '合同其他'), ('invoice_source', '发票源文件')], help_text='附件分类，根据关联对象类型选择相应的类型', max_length=50, verbose_name='附件类别'),
        ),
        migrations.AlterField(
            model_name='contract',
            name='total_amount',
            field=models.DecimalField(blank=True, decimal_places=2, max_digits=15, null=True, verbose_name='合同总额'),
        ),
    ]
