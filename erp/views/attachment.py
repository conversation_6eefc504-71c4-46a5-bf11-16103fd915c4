import os
import uuid
import hashlib
from django.db.models import Q
from django.contrib.contenttypes.models import ContentType
from django.http import HttpResponse, JsonResponse
from rest_framework import viewsets, status
from rest_framework.decorators import action
from rest_framework.response import Response
from rest_framework.parsers import <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, FormParser
from django.http import Http404
from drf_yasg.utils import swagger_auto_schema
from drf_yasg import openapi
from erp.models import Attachment
from erp.serializers.attachment import (
    AttachmentSerializer,
    AttachmentUploadSerializer,
    AttachmentQuerySerializer,
    AttachmentUpdateSerializer
)
from erp.utils.json_response import ListResponse, ErrorResponse, DetailResponse, CreatedResponse, StandardResponse
from erp.utils.pagination import CustomPagination
from erp.utils.viewsets import CreateUpdateMixin
from erp.utils.decorators import JWTRequiredMixin
from erp.utils.storage import attachment_upload_manager
from erp.swagger.response_schemas import (
    attachment_list_responses, attachment_retrieve_responses, attachment_upload_responses,
    attachment_delete_responses, attachment_download_responses,
    attachment_category_choices_responses
)
import logging

logger = logging.getLogger(__name__)


class AttachmentViewSet(JWTRequiredMixin, CreateUpdateMixin, viewsets.ModelViewSet):
    """附件管理视图集"""
    pagination_class = CustomPagination
    http_method_names = ['get', 'post', 'delete', 'head', 'options']
    filter_backends = []  # 明确设置为空，避免继承默认的过滤器

    def get_pagination_class(self):
        """根据action动态返回分页类"""
        if self.action in ['category_choices', 'download', 'upload']:
            return None
        return self.pagination_class

    def paginate_queryset(self, queryset):
        """重写分页方法，对特定action禁用分页"""
        if self.action in ['category_choices', 'download', 'upload']:
            return None
        return super().paginate_queryset(queryset)

    @property
    def paginator(self):
        """动态返回分页器"""
        if self.action in ['category_choices', 'download', 'upload']:
            return None
        return super().paginator

    def get_queryset(self):
        """获取查询集，只返回未删除的记录"""
        return Attachment.objects.filter(delete_datetime__isnull=True).order_by('-create_datetime')

    def get_serializer_class(self):
        """根据动作选择序列化器"""
        if self.action == 'upload':
            return AttachmentUploadSerializer
        elif self.action in ['update', 'partial_update']:
            return AttachmentUpdateSerializer
        return AttachmentSerializer

    @swagger_auto_schema(auto_schema=None)  # 在Swagger中隐藏此接口
    def create(self, request, *args, **kwargs):
        """禁用标准创建接口，附件应通过上传接口创建"""
        return ErrorResponse(
            msg="请使用 /attachments/upload/ 接口上传文件",
            code=4005
        )

    @swagger_auto_schema(auto_schema=None)  # 在Swagger中隐藏此接口
    def update(self, request, *args, **kwargs):
        """禁用PUT方法，只允许PATCH更新"""
        return ErrorResponse(
            msg="不支持PUT方法，请使用PATCH方法进行部分更新",
            code=4005
        )

    @swagger_auto_schema(
        operation_summary="获取附件列表",
        operation_description="获取附件列表，支持分页和筛选",
        manual_parameters=[
            openapi.Parameter('content_type', openapi.IN_QUERY, description="关联模型类型", type=openapi.TYPE_STRING, enum=['contract', 'project', 'customer']),
            openapi.Parameter('object_id', openapi.IN_QUERY, description="关联对象ID", type=openapi.TYPE_STRING),
            openapi.Parameter('category', openapi.IN_QUERY, description="附件类别筛选", type=openapi.TYPE_STRING, enum=[choice[0] for choice in Attachment.CATEGORY_CHOICES]),
            openapi.Parameter('file_type', openapi.IN_QUERY, description="文件类型筛选", type=openapi.TYPE_STRING, enum=['image', 'document', 'archive']),
            openapi.Parameter('search', openapi.IN_QUERY, description="文件名搜索", type=openapi.TYPE_STRING),
        ],
        responses=attachment_list_responses
    )
    def list(self, request):
        """获取附件列表"""
        try:
            # 获取查询参数
            query_serializer = AttachmentQuerySerializer(data=request.query_params)
            if not query_serializer.is_valid():
                return ErrorResponse(msg="查询参数错误", data=query_serializer.errors)

            params = query_serializer.validated_data
            queryset = self.get_queryset()

            # 按关联对象筛选
            if params.get('content_type') and params.get('object_id'):
                try:
                    content_type = ContentType.objects.get(model=params['content_type'])
                    queryset = queryset.filter(
                        content_type=content_type,
                        object_id=params['object_id']
                    )
                except ContentType.DoesNotExist:
                    return ErrorResponse(msg="无效的内容类型")

            # 附件类别筛选
            if params.get('category'):
                queryset = queryset.filter(category=params['category'])

            # 文件类型筛选
            if params.get('file_type'):
                file_type = params['file_type']
                if file_type == 'image':
                    queryset = queryset.filter(file_extension__in=['.jpg', '.jpeg', '.png', '.gif', '.bmp'])
                elif file_type == 'document':
                    queryset = queryset.filter(file_extension__in=['.pdf', '.doc', '.docx', '.xls', '.xlsx', '.ppt', '.pptx'])
                elif file_type == 'archive':
                    queryset = queryset.filter(file_extension__in=['.zip', '.rar', '.7z'])

            # 文件名搜索
            if params.get('search'):
                search = params['search']
                queryset = queryset.filter(
                    Q(original_name__icontains=search) |
                    Q(description__icontains=search)
                )

            # 分页
            page = self.paginate_queryset(queryset)
            if page is not None:
                serializer = self.get_serializer(page, many=True)
                return self.get_paginated_response(serializer.data)

            serializer = self.get_serializer(queryset, many=True)
            return ListResponse(data=serializer.data, msg="附件列表获取成功")

        except Exception as e:
            logger.error(f"获取附件列表失败: {str(e)}")
            return ErrorResponse(msg=f"获取附件列表失败: {str(e)}")

    @swagger_auto_schema(
        operation_summary="获取附件详情",
        operation_description="根据ID获取附件详细信息",
        responses=attachment_retrieve_responses
    )
    def retrieve(self, request, pk=None):
        """获取附件详情"""
        try:
            attachment = self.get_object()
            serializer = AttachmentSerializer(attachment)
            return DetailResponse(data=serializer.data, msg="附件详情获取成功")
        except (Attachment.DoesNotExist, Http404):
            return ErrorResponse(msg="该附件不存在", code=4004)
        except Exception as e:
            logger.error(f"获取附件详情失败: {str(e)}")
            return ErrorResponse(msg="获取附件详情失败，请稍后重试")

    @action(detail=False, methods=['post'], parser_classes=[MultiPartParser, FormParser])
    @swagger_auto_schema(
        operation_summary="上传附件",
        operation_description="单文件上传，支持小文件直接上传",
        request_body=AttachmentUploadSerializer,
        responses=attachment_upload_responses
    )
    def upload(self, request):
        """单文件上传"""
        try:
            serializer = AttachmentUploadSerializer(data=request.data)
            if not serializer.is_valid():
                return ErrorResponse(msg=serializer.errors)

            file_obj = serializer.validated_data['file']
            content_type_name = serializer.validated_data['content_type']
            object_id = serializer.validated_data['object_id']
            category = serializer.validated_data.get('category')
            description = serializer.validated_data.get('description', '')

            # 生成文件路径和名称
            file_path, file_name = attachment_upload_manager.generate_file_path(
                content_type_name, file_obj.name, object_id
            )

            # 计算文件MD5
            file_md5 = attachment_upload_manager.calculate_md5(file_obj)

            # 检查文件是否已存在（去重）
            existing_attachment = Attachment.objects.filter(
                file_md5=file_md5,
                delete_datetime__isnull=True
            ).first()

            if existing_attachment:
                # 文件已存在，创建新的附件记录但指向同一个文件
                file_path = existing_attachment.file_path
                file_name = existing_attachment.file_name
            else:
                # 上传新文件
                attachment_upload_manager.save_file(file_obj, file_path)

            # 获取ContentType
            content_type = ContentType.objects.get(model=content_type_name)

            # 创建附件记录
            attachment = Attachment.objects.create(
                original_name=file_obj.name,
                file_name=file_name,
                file_path=file_path,
                file_size=file_obj.size,
                file_type=file_obj.content_type,
                file_extension=os.path.splitext(file_obj.name)[1].lower(),
                file_md5=file_md5,
                content_type=content_type,
                object_id=object_id,
                category=category,
                description=description,
                creator=getattr(request.user, 'username', 'system') if hasattr(request, 'user') else 'system'
            )

            # 返回附件信息
            response_serializer = AttachmentSerializer(attachment)
            return CreatedResponse(data=response_serializer.data, msg="文件上传成功")

        except Exception as e:
            logger.error(f"文件上传失败: {str(e)}")
            return ErrorResponse(msg=f"文件上传失败: {str(e)}")



    # @action(detail=True, methods=['get'])
    @action(detail=False, methods=['get'])
    @swagger_auto_schema(
        operation_summary="下载附件",
        operation_description="通过文件路径下载附件",
        manual_parameters=[
            openapi.Parameter(
                'file_path',
                openapi.IN_QUERY,
                description="文件路径",
                type=openapi.TYPE_STRING,
                required=True
            )
        ],
        responses=attachment_download_responses
    )
    def download(self, request):
        """下载附件"""
        try:
            file_path = request.GET.get('file_path')
            if not file_path:
                return ErrorResponse(msg="缺少文件路径参数", code=4000)

            # 获取附件记录
            attachment = Attachment.objects.filter(
                file_path=file_path,
                delete_datetime__isnull=True
            ).first()

            if not attachment:
                return ErrorResponse(msg="附件不存在", code=4004)

            # 构建完整文件路径
            storage = attachment_upload_manager.storage
            full_path = os.path.join(storage.base_path, file_path)

            if not os.path.exists(full_path):
                return ErrorResponse(msg="文件不存在", code=4004)

            # 读取文件并返回
            with open(full_path, 'rb') as f:
                response = HttpResponse(f.read(), content_type=attachment.file_type or 'application/octet-stream')
                response['Content-Disposition'] = f'attachment; filename="{attachment.original_name}"'
                response['Content-Length'] = attachment.file_size
                return response

        except Exception as e:
            logger.error(f"下载附件失败: {str(e)}")
            return ErrorResponse(msg="下载附件失败，请稍后重试")

    @swagger_auto_schema(
        operation_summary="更新附件信息",
        operation_description="更新附件描述等信息",
        request_body=AttachmentUpdateSerializer,
        responses=attachment_retrieve_responses
    )
    def partial_update(self, request, pk=None):
        """更新附件信息"""
        try:
            attachment = self.get_object()
            serializer = AttachmentUpdateSerializer(attachment, data=request.data, partial=True)
            if not serializer.is_valid():
                return ErrorResponse(msg=serializer.errors)

            # 设置更新者
            if hasattr(request, 'user') and request.user.is_authenticated:
                serializer.validated_data['updater'] = request.user.username

            attachment = serializer.save()
            response_serializer = AttachmentSerializer(attachment)
            return DetailResponse(data=response_serializer.data, msg="附件信息更新成功")

        except (Attachment.DoesNotExist, Http404):
            return ErrorResponse(msg="该附件不存在", code=4004)
        except Exception as e:
            logger.error(f"更新附件失败: {str(e)}")
            return ErrorResponse(msg="更新附件失败，请稍后重试")

    @swagger_auto_schema(
        operation_summary="删除附件",
        operation_description="软删除附件记录",
        responses=attachment_delete_responses
    )
    def destroy(self, request, pk=None):
        """软删除附件"""
        try:
            attachment = self.get_object()
            
            # 执行软删除
            attachment.delete()
            
            return StandardResponse(msg="附件删除成功")

        except (Attachment.DoesNotExist, Http404):
            return ErrorResponse(msg="该附件不存在", code=4004)
        except Exception as e:
            logger.error(f"删除附件失败: {str(e)}")
            return ErrorResponse(msg="删除附件失败，请稍后重试")

    @action(detail=False, methods=['get'], url_path='category-choices')
    @swagger_auto_schema(
        operation_id='attachment_category_choices',
        operation_summary="获取附件类别选项",
        operation_description="根据关联对象类型获取可用的附件类别选项。这是一个简单的工具接口，返回静态的类别选项，不支持分页和过滤。",
        manual_parameters=[
            openapi.Parameter(
                'content_type',
                openapi.IN_QUERY,
                description="关联模型类型，可选值：project, contract",
                type=openapi.TYPE_STRING,
                enum=['project', 'contract'],
                required=False
            )
        ],
        responses={
            200: openapi.Response(
                description="获取成功",
                schema=openapi.Schema(
                    type=openapi.TYPE_OBJECT,
                    properties={
                        'code': openapi.Schema(type=openapi.TYPE_INTEGER, description='响应代码'),
                        'msg': openapi.Schema(type=openapi.TYPE_STRING, description='响应消息'),
                        'data': openapi.Schema(
                            type=openapi.TYPE_OBJECT,
                            properties={
                                'all_choices': openapi.Schema(
                                    type=openapi.TYPE_ARRAY,
                                    items=openapi.Schema(
                                        type=openapi.TYPE_OBJECT,
                                        properties={
                                            'value': openapi.Schema(type=openapi.TYPE_STRING),
                                            'label': openapi.Schema(type=openapi.TYPE_STRING)
                                        }
                                    )
                                ),
                                'project_choices': openapi.Schema(
                                    type=openapi.TYPE_ARRAY,
                                    items=openapi.Schema(
                                        type=openapi.TYPE_OBJECT,
                                        properties={
                                            'value': openapi.Schema(type=openapi.TYPE_STRING),
                                            'label': openapi.Schema(type=openapi.TYPE_STRING)
                                        }
                                    )
                                ),
                                'contract_choices': openapi.Schema(
                                    type=openapi.TYPE_ARRAY,
                                    items=openapi.Schema(
                                        type=openapi.TYPE_OBJECT,
                                        properties={
                                            'value': openapi.Schema(type=openapi.TYPE_STRING),
                                            'label': openapi.Schema(type=openapi.TYPE_STRING)
                                        }
                                    )
                                )
                            }
                        )
                    }
                ),
                examples={
                    "application/json": {
                        "code": 200,
                        "msg": "获取成功",
                        "data": {
                            "all_choices": [
                                {"value": "project_trial_balance", "label": "试算表"},
                                {"value": "project_other", "label": "项目其他"}
                            ],
                            "project_choices": [
                                {"value": "project_trial_balance", "label": "试算表"},
                                {"value": "project_other", "label": "项目其他"}
                            ],
                            "contract_choices": [
                                {"value": "contract_signed_scan", "label": "合同双章扫描件"},
                                {"value": "contract_final_word", "label": "合同终稿word版"}
                            ]
                        }
                    }
                }
            )
        },
        tags=['attachments']
    )
    def category_choices(self, request):
        """获取附件类别选项"""
        try:
            content_type = request.query_params.get('content_type')

            # 获取所有选项
            all_choices = [{"value": choice[0], "label": choice[1]} for choice in Attachment.CATEGORY_CHOICES]

            # 获取项目附件选项
            project_choices = [{"value": choice[0], "label": choice[1]} for choice in Attachment.get_project_category_choices()]

            # 获取合同附件选项
            contract_choices = [{"value": choice[0], "label": choice[1]} for choice in Attachment.get_contract_category_choices()]

            data = {
                "all_choices": all_choices,
                "project_choices": project_choices,
                "contract_choices": contract_choices
            }

            # 如果指定了content_type，只返回对应的选项
            if content_type == 'project':
                data = {"choices": project_choices}
            elif content_type == 'contract':
                data = {"choices": contract_choices}

            return StandardResponse(data=data, msg="获取附件类别选项成功")

        except Exception as e:
            logger.error(f"获取附件类别选项失败: {str(e)}")
            return ErrorResponse(msg="获取附件类别选项失败，请稍后重试")


