from django.db import models
from django.core.exceptions import ValidationError
from .base import CoreModel, SoftDeleteDatetimeModel
from .partner import Partner
from .project import Project
from .custom_field import CustomFieldValue


class Contract(CoreModel, SoftDeleteDatetimeModel):
    """合同模型"""

    # 合同类别选择
    CATEGORY_CHOICES = [
        ('sales', '销售合同'),
        ('procurement', '采购合同'),
    ]

    # 签订状态选择（根据需求文件更新）
    SIGN_STATUS_CHOICES = [
        ('unsigned', '未签约'),
        ('communicating', '沟通中'),
        ('signing', '签约中'),
        ('signed', '已签约'),
        ('terminating', '解约中'),
    ]

    # 履约状态选择（根据需求文件更新）
    PERFORMANCE_STATUS_CHOICES = [
        ('not_performed', '未履约'),
        ('performing', '履约中'),
        ('performed', '已履约'),
    ]

    # 基本信息
    code = models.CharField(max_length=50, verbose_name="合同编号")  # 允许修改但保持唯一性
    name = models.CharField(max_length=200, verbose_name="合同名称")
    category = models.CharField(max_length=20, choices=CATEGORY_CHOICES, verbose_name="合同类型")

    # 新的状态字段（根据需求文件）
    sign_status = models.CharField(
        max_length=20,
        choices=SIGN_STATUS_CHOICES,
        default='unsigned',
        verbose_name="签订状态"
    )
    performance_status = models.CharField(
        max_length=20,
        choices=PERFORMANCE_STATUS_CHOICES,
        default='not_performed',
        verbose_name="履约状态"
    )

    # 新增字段
    sales_manager = models.CharField(max_length=100, null=True, blank=True, verbose_name="销售负责人")
    effective_date = models.DateField(null=True, blank=True, verbose_name="生效日期")
    termination_date = models.DateField(null=True, blank=True, verbose_name="终止日期")

    # 关联信息
    project = models.ForeignKey(
        Project,
        on_delete=models.PROTECT,
        related_name='contracts',
        verbose_name="所属项目"
    )

    # 相对方（支持多选，例如三方合同）
    partners = models.ManyToManyField(
        Partner,
        related_name='contracts',
        verbose_name="相对方",
        help_text="支持多选，例如三方合同"
    )

    # 金额和日期
    total_amount = models.DecimalField(max_digits=15, decimal_places=2, null=True, blank=True, verbose_name="合同总额")
    sign_date = models.DateField(null=True, blank=True, verbose_name="签订日期")

    # 其他信息
    remark = models.TextField(null=True, blank=True, verbose_name="备注")

    class Meta:
        db_table = 'erp_contract'
        verbose_name = '合同'
        verbose_name_plural = '合同'
        ordering = ['-create_datetime']
        indexes = [
            models.Index(fields=['code']),
            models.Index(fields=['name']),
            models.Index(fields=['category']),
            models.Index(fields=['sign_status']),
            models.Index(fields=['performance_status']),
            models.Index(fields=['create_datetime']),
        ]
        constraints = [
            models.UniqueConstraint(
                fields=['code'],
                condition=models.Q(delete_datetime__isnull=True),
                name='unique_contract_code_when_not_deleted'
            )
        ]

    def __str__(self):
        return f"{self.code} - {self.name}"

    def save(self, *args, **kwargs):
        """保存时自动生成合同编号并验证数据"""
        if not self.code:
            # 生成合同编号：[合同类型前缀][项目内流水号]-[项目编码]
            if not self.project_id:
                raise ValidationError("合同必须关联项目才能生成编号")

            # 确定合同类型前缀
            if self.category == 'sales':
                prefix = 'SAL'
            elif self.category == 'procurement':
                prefix = 'PUR'
            else:
                raise ValidationError("无效的合同类别")

            # 查找同一项目下该类型合同的最大序号
            last_contract = Contract.objects.filter(
                project_id=self.project_id,
                category=self.category,
                delete_datetime__isnull=True  # 只查询未删除的记录
            ).order_by('-code').first()

            if last_contract and last_contract.code.startswith(prefix):
                # 提取序号部分（前缀后的3位数字）
                try:
                    last_seq = int(last_contract.code[3:6])
                    new_seq = last_seq + 1
                except (ValueError, IndexError):
                    new_seq = 1
            else:
                new_seq = 1

            # 生成完整编号：SAL001-P001-C250606001 或 PUR001-P001-C250606001
            self.code = f'{prefix}{new_seq:03d}-{self.project.code}'

        # 验证数据
        self._validate_basic_requirements()
        self._validate_dates()
        self._validate_name_uniqueness()
        self._validate_code_uniqueness()

        super().save(*args, **kwargs)

    def _validate_basic_requirements(self):
        """验证基本要求"""
        # 合同必须关联项目
        if not self.project_id:
            raise ValidationError("合同必须关联项目")

        # 验证合同类别
        if self.category not in ['sales', 'procurement']:
            raise ValidationError("合同必须指定有效的类别")

    def _validate_dates(self):
        """验证日期逻辑"""
        if self.effective_date and self.termination_date and self.effective_date > self.termination_date:
            raise ValidationError("生效日期不能晚于终止日期")

        if self.sign_date and self.effective_date and self.sign_date > self.effective_date:
            raise ValidationError("签订日期不能晚于生效日期")

    def _validate_name_uniqueness(self):
        """验证合同名称在未删除记录中的唯一性"""
        existing = Contract.objects.filter(
            name=self.name,
            delete_datetime__isnull=True  # 只检查未删除的记录
        ).exclude(id=self.id)  # 排除自己

        if existing.exists():
            raise ValidationError(f"合同名称 {self.name} 已存在")

    def _validate_code_uniqueness(self):
        """验证合同编号在未删除记录中的唯一性"""
        existing = Contract.objects.filter(
            code=self.code,
            delete_datetime__isnull=True  # 只检查未删除的记录
        ).exclude(id=self.id)  # 排除自己

        if existing.exists():
            raise ValidationError(f"合同编号 {self.code} 已存在")

    @property
    def display_name(self):
        """显示名称"""
        return f"{self.name}({self.code})"



    @property
    def category_display(self):
        """合同类别显示名称"""
        return dict(self.CATEGORY_CHOICES).get(self.category, self.category)

    @property
    def sign_status_display(self):
        """签订状态显示名称"""
        return dict(self.SIGN_STATUS_CHOICES).get(self.sign_status, self.sign_status)

    @property
    def performance_status_display(self):
        """履约状态显示名称"""
        return dict(self.PERFORMANCE_STATUS_CHOICES).get(self.performance_status, self.performance_status)

    def get_contract_duration_days(self):
        """获取合同期限天数"""
        if self.effective_date and self.termination_date:
            return (self.termination_date - self.effective_date).days
        return None

    def is_expired(self):
        """判断合同是否已过期"""
        if self.termination_date:
            from django.utils import timezone
            return self.termination_date < timezone.now().date()
        return False

    def get_partners_display(self):
        """获取相对方显示名称"""
        return ', '.join([partner.name for partner in self.partners.all()])

    def get_required_attachment_categories(self, for_execution=True):
        """获取合同必需的附件类别

        Args:
            for_execution (bool): True表示执行时的要求，False表示完成时的要求
        """
        # 通用必需附件
        required_categories = ['contract_signed_scan']  # 合同双章扫描件必填

        # 根据合同类别添加特定必需附件
        if self.category == 'sales':
            required_categories.append('sales_invoice_scan')  # 销售发票扫描件
            if not for_execution:
                # 完成时需要签收单或验收报告（二选一）
                required_categories.append('receipt_confirmation_or_acceptance_report')
        elif self.category == 'procurement':
            required_categories.append('purchase_invoice_scan')  # 采购发票扫描件

        return required_categories

    def has_all_required_attachments(self, for_execution=True):
        """检查是否已上传所有必需的附件

        Args:
            for_execution (bool): True表示检查执行时的要求，False表示检查完成时的要求
        """
        from django.contrib.contenttypes.models import ContentType
        from .attachment import Attachment

        required_categories = self.get_required_attachment_categories(for_execution=for_execution)

        # 获取合同的所有附件
        content_type = ContentType.objects.get_for_model(Contract)
        existing_attachments = Attachment.objects.filter(
            content_type=content_type,
            object_id=self.id,
            delete_datetime__isnull=True
        ).values_list('category', flat=True)

        # 检查是否所有必需的附件类别都已上传
        for category in required_categories:
            if category == 'receipt_confirmation_or_acceptance_report':
                # 特殊处理：签收单或验收报告二选一
                if not ('receipt_confirmation' in existing_attachments or 'acceptance_report' in existing_attachments):
                    return False
            else:
                if category not in existing_attachments:
                    return False

        return True

    def get_missing_attachments(self, for_execution=True):
        """获取缺失的必需附件信息

        Args:
            for_execution (bool): True表示检查执行时的要求，False表示检查完成时的要求

        Returns:
            list: 缺失的附件类别及其显示名称
        """
        from django.contrib.contenttypes.models import ContentType
        from .attachment import Attachment

        required_categories = self.get_required_attachment_categories(for_execution=for_execution)

        # 获取合同的所有附件
        content_type = ContentType.objects.get_for_model(Contract)
        existing_attachments = Attachment.objects.filter(
            content_type=content_type,
            object_id=self.id,
            delete_datetime__isnull=True
        ).values_list('category', flat=True)

        # 附件类别显示名称映射
        category_display_map = {
            'contract_signed_scan': '合同双章扫描件',
            'sales_invoice_scan': '销售发票扫描件',
            'purchase_invoice_scan': '采购发票扫描件',
            'receipt_confirmation': '签收单',
            'acceptance_report': '验收报告',
            'receipt_confirmation_or_acceptance_report': '签收单或验收报告'
        }

        missing_attachments = []

        # 检查缺失的附件
        for category in required_categories:
            if category == 'receipt_confirmation_or_acceptance_report':
                # 特殊处理：签收单或验收报告二选一
                if not ('receipt_confirmation' in existing_attachments or 'acceptance_report' in existing_attachments):
                    missing_attachments.append({
                        'category': category,
                        'display_name': category_display_map.get(category, category)
                    })
            else:
                if category not in existing_attachments:
                    missing_attachments.append({
                        'category': category,
                        'display_name': category_display_map.get(category, category)
                    })

        return missing_attachments

    def is_signed(self):
        """判断是否已签约"""
        return self.sign_status == 'signed'

    def is_performing(self):
        """判断是否履约中"""
        return self.performance_status == 'performing'

    def is_performed(self):
        """判断是否已履约"""
        return self.performance_status == 'performed'

    def can_sign(self):
        """判断是否可以签约"""
        return self.sign_status in ['unsigned', 'communicating', 'signing']

    def can_perform(self):
        """判断是否可以履约"""
        return self.sign_status == 'signed' and self.performance_status == 'not_performed'

    def get_custom_field_values(self):
        """获取自定义字段值"""
        return CustomFieldValue.get_values_for_object(self.id, 'contract')

    def set_custom_field_values(self, custom_fields_data, user=None):
        """设置自定义字段值"""
        CustomFieldValue.set_values_for_object(
            self.id, 'contract', custom_fields_data, user
        )
