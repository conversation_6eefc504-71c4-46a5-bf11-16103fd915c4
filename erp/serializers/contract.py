from rest_framework import serializers
from erp.models.contract import Contract
from erp.models.custom_field import CustomFieldValue


class ContractSerializer(serializers.ModelSerializer):
    """合同序列化器"""

    # 自定义时间格式
    create_datetime = serializers.DateTimeField(format='%Y-%m-%d %H:%M:%S', read_only=True)
    update_datetime = serializers.DateTimeField(format='%Y-%m-%d %H:%M:%S', read_only=True)
    sign_date = serializers.DateField(format='%Y-%m-%d', required=False, allow_null=True)
    effective_date = serializers.DateField(format='%Y-%m-%d', required=False, allow_null=True)
    termination_date = serializers.DateField(format='%Y-%m-%d', required=False, allow_null=True)

    # 自定义字段
    custom_fields = serializers.ListField(
        child=serializers.DictField(),
        required=False,
        write_only=True,
        help_text="自定义字段值，格式：[{\"field_name\": \"字段名称\", \"value\": \"字段值\"}]"
    )

    class Meta:
        model = Contract
        fields = [
            'id', 'code', 'name', 'category', 'sign_status', 'performance_status',
            'project', 'partners', 'total_amount', 'sales_manager',
            'sign_date', 'effective_date', 'termination_date', 'remark',
            'custom_fields', 'create_datetime', 'update_datetime'
        ]
        read_only_fields = ['id', 'create_datetime', 'update_datetime']

    def to_representation(self, instance):
        """自定义序列化输出"""
        data = super().to_representation(instance)

        # 添加显示名称
        data['category_display'] = instance.category_display
        data['sign_status_display'] = instance.sign_status_display
        data['performance_status_display'] = instance.performance_status_display

        # 添加关联对象信息
        if instance.project:
            data['project_name'] = instance.project.name
            data['project_code'] = instance.project.code

        # 添加相对方信息
        partners_data = []
        for partner in instance.partners.all():
            partners_data.append({
                'id': partner.id,
                'code': partner.code,
                'name': partner.name,
                'partner_type': partner.partner_type,
                'partner_type_display': partner.get_partner_type_display(),
            })
        data['partners_info'] = partners_data

        # 添加便捷方法结果
        data['contract_duration_days'] = instance.get_contract_duration_days()
        data['is_expired'] = instance.is_expired()

        # 添加自定义字段值
        data['custom_field_values'] = instance.get_custom_field_values()

        return data


class ContractListSerializer(serializers.ModelSerializer):
    """合同列表序列化器（完整版）"""

    # 自定义时间格式
    create_datetime = serializers.DateTimeField(format='%Y-%m-%d %H:%M:%S', read_only=True)
    update_datetime = serializers.DateTimeField(format='%Y-%m-%d %H:%M:%S', read_only=True)
    sign_date = serializers.DateField(format='%Y-%m-%d', read_only=True)
    effective_date = serializers.DateField(format='%Y-%m-%d', read_only=True)
    termination_date = serializers.DateField(format='%Y-%m-%d', read_only=True)

    class Meta:
        model = Contract
        fields = [
            'id', 'code', 'name', 'category', 'sign_status', 'performance_status',
            'project', 'partners', 'total_amount', 'sales_manager',
            'sign_date', 'effective_date', 'termination_date', 'remark',
            'create_datetime', 'update_datetime', 'creator', 'updater'
        ]

    def to_representation(self, instance):
        """自定义序列化输出"""
        data = super().to_representation(instance)

        # 添加显示名称
        data['category_display'] = instance.category_display
        data['sign_status_display'] = instance.sign_status_display
        data['performance_status_display'] = instance.performance_status_display

        # 添加关联对象信息
        if instance.project:
            data['project_name'] = instance.project.name
            data['project_code'] = instance.project.code
            data['project_type'] = instance.project.type
            data['project_type_display'] = instance.project.get_type_display()

        # 添加相对方信息
        partners_data = []
        for partner in instance.partners.all():
            partners_data.append({
                'id': partner.id,
                'code': partner.code,
                'name': partner.name,
                'partner_type': partner.partner_type,
                'partner_type_display': partner.get_partner_type_display(),
            })
        data['partners_info'] = partners_data
        data['partners_display'] = instance.get_partners_display()

        # 添加便捷方法结果
        data['contract_duration_days'] = instance.get_contract_duration_days()
        data['is_expired'] = instance.is_expired()

        # 添加自定义字段值
        data['custom_field_values'] = instance.get_custom_field_values()

        return data


class ContractCreateSerializer(serializers.ModelSerializer):
    """合同创建序列化器"""

    # 优化参数名称，使用ID后缀
    project_id = serializers.UUIDField(write_only=True)
    partner_ids = serializers.ListField(
        child=serializers.UUIDField(),
        write_only=True,
        help_text="相对方ID列表，支持多选"
    )

    # 自定义时间格式
    sign_date = serializers.DateField(format='%Y-%m-%d', required=False, allow_null=True)
    effective_date = serializers.DateField(format='%Y-%m-%d', required=False, allow_null=True)
    termination_date = serializers.DateField(format='%Y-%m-%d', required=False, allow_null=True)

    # 自定义字段
    custom_fields = serializers.ListField(
        child=serializers.DictField(),
        required=False,
        write_only=True,
        help_text="自定义字段值，格式：[{\"field_name\": \"字段名称\", \"value\": \"字段值\"}]"
    )

    # 合同金额设为选填
    total_amount = serializers.DecimalField(
        max_digits=15,
        decimal_places=2,
        required=False,
        allow_null=True,
        help_text="合同总额，选填"
    )

    class Meta:
        model = Contract
        fields = [
            'name', 'category', 'project_id', 'partner_ids', 'total_amount', 'sales_manager',
            'sign_date', 'effective_date', 'termination_date', 'remark', 'custom_fields'
        ]

    def validate(self, attrs):
        """自定义验证"""
        from erp.models.project import Project
        from erp.models.partner import Partner

        project_id = attrs.get('project_id')
        partner_ids = attrs.get('partner_ids', [])

        # 验证项目必须存在
        if not project_id:
            raise serializers.ValidationError("合同必须关联项目")

        # 验证项目是否存在
        if not Project.objects.filter(id=project_id, delete_datetime__isnull=True).exists():
            raise serializers.ValidationError("项目不存在")

        # 验证相对方
        if not partner_ids:
            raise serializers.ValidationError("合同必须关联至少一个相对方")

        # 验证所有相对方是否存在
        existing_partners = Partner.objects.filter(
            id__in=partner_ids,
            delete_datetime__isnull=True
        ).count()

        if existing_partners != len(partner_ids):
            raise serializers.ValidationError("部分相对方不存在")

        return attrs

    def create(self, validated_data):
        """创建合同，支持自定义字段"""
        # 提取相对方ID和自定义字段数据
        partner_ids = validated_data.pop('partner_ids', [])
        custom_fields_data = validated_data.pop('custom_fields', [])
        project_id = validated_data.pop('project_id', None)

        # 设置项目
        if project_id:
            validated_data['project_id'] = project_id

        # 创建合同实例
        contract = Contract(**validated_data)
        contract.save()

        # 设置相对方关系
        if partner_ids:
            contract.partners.set(partner_ids)

        # 保存自定义字段值
        if custom_fields_data:
            creator = validated_data.get('creator', None)
            CustomFieldValue.set_values_for_object(
                contract.id, 'contract', custom_fields_data, creator
            )

        return contract


class ContractUpdateSerializer(serializers.ModelSerializer):
    """合同完全更新序列化器（PUT）"""

    # 自定义时间格式
    sign_date = serializers.DateField(format='%Y-%m-%d', required=False, allow_null=True)
    effective_date = serializers.DateField(format='%Y-%m-%d', required=False, allow_null=True)
    termination_date = serializers.DateField(format='%Y-%m-%d', required=False, allow_null=True)

    # 自定义字段
    custom_fields = serializers.ListField(
        child=serializers.DictField(),
        required=False,
        write_only=True,
        help_text="自定义字段值，格式：[{\"field_name\": \"字段名称\", \"value\": \"字段值\"}]"
    )

    class Meta:
        model = Contract
        fields = [
            'code', 'name', 'total_amount', 'sales_manager', 'sign_status', 'performance_status',
            'sign_date', 'effective_date', 'termination_date', 'remark', 'custom_fields'
        ]
        read_only_fields = ['category', 'project', 'partners']  # 允许修改合同编号

    def update(self, instance, validated_data):
        """更新合同，支持自定义字段"""
        # 提取自定义字段数据
        custom_fields_data = validated_data.pop('custom_fields', [])

        # 更新基础字段
        for attr, value in validated_data.items():
            setattr(instance, attr, value)
        instance.save()

        # 更新自定义字段值
        if custom_fields_data:
            updater = getattr(instance, 'updater', None)
            CustomFieldValue.set_values_for_object(
                instance.id, 'contract', custom_fields_data, updater
            )

        return instance


class ContractPartialUpdateSerializer(serializers.ModelSerializer):
    """合同部分更新序列化器（PATCH）"""

    # 自定义时间格式
    sign_date = serializers.DateField(format='%Y-%m-%d', required=False, allow_null=True)
    effective_date = serializers.DateField(format='%Y-%m-%d', required=False, allow_null=True)
    termination_date = serializers.DateField(format='%Y-%m-%d', required=False, allow_null=True)

    # 自定义字段
    custom_fields = serializers.ListField(
        child=serializers.DictField(),
        required=False,
        write_only=True,
        help_text="自定义字段值，格式：[{\"field_name\": \"字段名称\", \"value\": \"字段值\"}]"
    )

    class Meta:
        model = Contract
        fields = [
            'code', 'name', 'total_amount', 'sales_manager', 'sign_status', 'performance_status',
            'sign_date', 'effective_date', 'termination_date', 'remark', 'custom_fields'
        ]
        read_only_fields = ['category', 'project', 'partners']  # 允许修改合同编号
        extra_kwargs = {
            'name': {'required': False},
            'total_amount': {'required': False},
        }

    def update(self, instance, validated_data):
        """更新合同，支持自定义字段"""
        # 提取自定义字段数据
        custom_fields_data = validated_data.pop('custom_fields', [])

        # 更新基础字段
        for attr, value in validated_data.items():
            setattr(instance, attr, value)
        instance.save()

        # 更新自定义字段值
        if custom_fields_data:
            updater = getattr(instance, 'updater', None)
            CustomFieldValue.set_values_for_object(
                instance.id, 'contract', custom_fields_data, updater
            )

        return instance


class ContractStatusUpdateSerializer(serializers.ModelSerializer):
    """合同状态更新序列化器"""

    class Meta:
        model = Contract
        fields = ['sign_status', 'performance_status']
        extra_kwargs = {
            'sign_status': {'required': False},
            'performance_status': {'required': False},
        }

    def validate(self, attrs):
        """验证状态更新"""
        # 至少需要更新一个状态字段
        if not attrs.get('sign_status') and not attrs.get('performance_status'):
            raise serializers.ValidationError("至少需要更新一个状态字段")

        return attrs


class ContractQuerySerializer(serializers.Serializer):
    """合同查询参数序列化器"""

    search = serializers.CharField(required=False, help_text="搜索关键词（合同名称、编号）")
    category = serializers.ChoiceField(choices=Contract.CATEGORY_CHOICES, required=False, help_text="合同类型")
    sign_status = serializers.ChoiceField(choices=Contract.SIGN_STATUS_CHOICES, required=False, help_text="签订状态")
    performance_status = serializers.ChoiceField(choices=Contract.PERFORMANCE_STATUS_CHOICES, required=False, help_text="履约状态")
    project_id = serializers.UUIDField(required=False, help_text="项目ID")
    partner_ids = serializers.CharField(required=False, help_text="相对方ID列表（逗号分隔）")
    total_amount_min = serializers.DecimalField(max_digits=15, decimal_places=2, required=False, help_text="最小金额")
    total_amount_max = serializers.DecimalField(max_digits=15, decimal_places=2, required=False, help_text="最大金额")
    sign_date_start = serializers.DateField(required=False, help_text="签订开始日期")
    sign_date_end = serializers.DateField(required=False, help_text="签订结束日期")
    effective_date_start = serializers.DateField(required=False, help_text="生效日期范围-开始")
    effective_date_end = serializers.DateField(required=False, help_text="生效日期范围-结束")
    termination_date_start = serializers.DateField(required=False, help_text="终止日期范围-开始")
    termination_date_end = serializers.DateField(required=False, help_text="终止日期范围-结束")
